import { cssInterop } from "nativewind";
import Svg, {
  Circle,
  Ellipse,
  G,
  Text,
  Path,
  Polygon,
  Polyline,
  Line,
  Rect,
} from "react-native-svg";
import { Feather } from "@expo/vector-icons";

// Enable NativeWind className support for react-native-svg components
// This allows SVG components to accept className prop and apply NativeWind styles

// @ts-ignore - NativeWind v4 cssInterop types may not be fully compatible with react-native-svg
cssInterop(Svg, {
  className: {
    target: "style",
    nativeStyleToProp: {
      width: "width",
      height: "height",
    },
  },
});
// @ts-ignore
cssInterop(G, { className: "style" });
// @ts-ignore
cssInterop(Path, { className: "style" });
// @ts-ignore
cssInterop(Circle, { className: "style" });
// @ts-ignore
cssInterop(Rect, { className: "style" });
// @ts-ignore
cssInterop(Ellipse, { className: "style" });
// @ts-ignore
cssInterop(Line, { className: "style" });
// @ts-ignore
cssInterop(Polyline, { className: "style" });
// @ts-ignore
cssInterop(Polygon, { className: "style" });
// @ts-ignore
cssInterop(Text, { className: "style" });

// Enable NativeWind className support for Feather icons
// @ts-ignore - NativeWind v4 cssInterop types may not be fully compatible with @expo/vector-icons
cssInterop(Feather, {
  className: {
    target: "style",
    nativeStyleToProp: {
      color: "color",
      fontSize: "size",
    },
  },
});
