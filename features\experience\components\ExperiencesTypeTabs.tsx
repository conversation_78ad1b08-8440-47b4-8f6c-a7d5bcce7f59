import React from "react";
import { ScrollView, Text, TouchableOpacity } from "react-native";
import { cn } from "@/lib/utils";
import {
  EXPERIENCE_TYPES,
  ExperienceTypeKey,
} from "@/features/experience/model";

const experienceTypes = Object.values(EXPERIENCE_TYPES);

type ExperiencesTypeTabsProps = {
  selectedType?: ExperienceTypeKey | "ALL";
  onTypeSelect?: (type: ExperienceTypeKey | "ALL") => void;
};

const ExperiencesTypeTabs = ({
  selectedType = "ALL",
  onTypeSelect,
}: ExperiencesTypeTabsProps) => {
  return (
    <ScrollView
      horizontal
      showsHorizontalScrollIndicator={false}
      contentContainerClassName="pr-4 gap-5"
      className="flex-grow-0"
    >
      {experienceTypes.map(({ label, value, Icon }) => {
        const isSelected = selectedType === value;

        return (
          <TouchableOpacity
            key={value}
            onPress={() => onTypeSelect?.(value)}
            className={cn(
              "flex-row items-center gap-2.5 px-4 py-1.5 rounded-full",
              isSelected ? "bg-primary-1" : "bg-light-primary"
            )}
          >
            {Icon && (
              <Icon
                className={cn(
                  "size-4",
                  isSelected ? "text-white" : "text-light-secondary"
                )}
              />
            )}
            <Text
              className={cn(
                "",
                isSelected ? "text-white" : "text-light-secondary"
              )}
            >
              {label}
            </Text>
          </TouchableOpacity>
        );
      })}
    </ScrollView>
  );
};

export default ExperiencesTypeTabs;
