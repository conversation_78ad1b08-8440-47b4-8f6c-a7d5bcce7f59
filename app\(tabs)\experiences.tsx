import React, { useState } from "react";
import { View } from "react-native";
import { useTranslation } from "react-i18next";
import Layout from "@/components/Layout";
import HeaderExperiencesFilter from "@/components/HeaderExperiencesFilter";
import ExperiencesTypeTabs from "@/features/experience/components/ExperiencesTypeTabs";
import ExperienceList from "@/features/experience/components/ExperienceList";
import { ExperienceTypeKey, ExperienceType } from "@/features/experience/model";

export default function ExperiencesScreen() {
  const { t } = useTranslation();
  const [selectedType, setSelectedType] = useState<ExperienceTypeKey>("ALL");
  const [searchValue, setSearchValue] = useState("");

  // Mock experiences data - replace with actual data fetching
  const [experiences, setExperiences] = useState<ExperienceType[]>([
    {
      _id: "1",
      _type: "experience",
      _createdAt: "2024-01-01T00:00:00Z",
      _updatedAt: "2024-01-01T00:00:00Z",
      name: "Resort Namibe",
      description:
        "O Resort Namibe oferece vistas deslumbrantes sobre o Oceano Atlântico, com acesso direto a praias de areia branca e acomodações de luxo.",
      type: "ACTIVITY",
      locations: [
        {
          name: "Ladeira, Bairro Novo",
          province: "namibe",
          mapLink: "https://maps.google.com",
        },
      ],
      medias: [],
      provider: null,
      features: null,
      details: null,
      prices: null,
      checkoutMethods: null,
      highlightedUntil: null,
      sponsoredUntil: null,
      availability: null,
      items: null,
    },
    {
      _id: "2",
      _type: "experience",
      _createdAt: "2024-01-01T00:00:00Z",
      _updatedAt: "2024-01-01T00:00:00Z",
      name: "Sabores do Oceano",
      description:
        "Sabores do Oceano oferece vistas deslumbrantes do Atlântico, com marisco fresco e uma experiência gastronômica requintada.",
      type: "RESTAURANT_BAR",
      locations: [
        {
          name: "Rua da Costa, Bairro das Ondas",
          province: "namibe",
          mapLink: "https://maps.google.com",
        },
      ],
      medias: [],
      provider: null,
      features: null,
      details: null,
      prices: null,
      checkoutMethods: null,
      highlightedUntil: null,
      sponsoredUntil: null,
      availability: null,
      items: null,
    },
    {
      _id: "3",
      _type: "experience",
      _createdAt: "2024-01-01T00:00:00Z",
      _updatedAt: "2024-01-01T00:00:00Z",
      name: "Hotel Praia Azul",
      description:
        "O Hotel Praia Azul proporciona uma experiência única com seus quartos de frente para o mar e um spa relaxante. Ideal para famílias e casais.",
      type: "LODGING",
      locations: [
        {
          name: "Avenida da Praia, Baía Azul",
          province: "namibe",
          mapLink: "https://maps.google.com",
        },
      ],
      medias: [],
      provider: null,
      features: null,
      details: null,
      prices: null,
      checkoutMethods: null,
      highlightedUntil: null,
      sponsoredUntil: null,
      availability: null,
      items: null,
    },
  ]);
  const [isLoading, setIsLoading] = useState(false);
  const [favoriteExperienceIds, setFavoriteExperienceIds] = useState<string[]>(
    []
  );

  const handleSearch = (text: string) => {
    setSearchValue(text);
    // TODO: Implement search functionality
  };

  const handleFilterPress = () => {
    // TODO: Implement filter functionality
  };

  const handleTypeSelect = (type: ExperienceTypeKey) => {
    setSelectedType(type);
    // TODO: Implement type filtering
  };

  const handleExperiencePress = (experience: ExperienceType) => {
    // TODO: Navigate to experience detail screen
    console.log("Experience pressed:", experience.name);
  };

  const handleFavoritePress = (experience: ExperienceType) => {
    // TODO: Implement favorite toggle functionality
    setFavoriteExperienceIds((prev) => {
      if (prev.includes(experience._id)) {
        return prev.filter((id) => id !== experience._id);
      } else {
        return [...prev, experience._id];
      }
    });
  };

  return (
    <>
      <Layout
        title={{ text: t("common.experiences") }}
        showSearch
        noScroll
        noMargin
        noPadding
        headerFilter={
          <View className="gap-5">
            <HeaderExperiencesFilter
              onSearch={handleSearch}
              onFilterPress={handleFilterPress}
              searchValue={searchValue}
              searchPlaceholder={t("common.search")}
            />
            <ExperiencesTypeTabs
              selectedType={selectedType}
              onTypeSelect={handleTypeSelect}
            />
          </View>
        }
      >
        <ExperienceList
          experiences={experiences}
          isLoading={isLoading}
          onExperiencePress={handleExperiencePress}
          onFavoritePress={handleFavoritePress}
          favoriteExperienceIds={favoriteExperienceIds}
        />
      </Layout>
    </>
  );
}
