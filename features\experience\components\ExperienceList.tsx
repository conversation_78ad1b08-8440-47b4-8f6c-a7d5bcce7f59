import React from "react";
import {
  FlatList,
  Text,
  View,
  RefreshControl,
  ActivityIndicator,
} from "react-native";
import { cn } from "@/lib/utils";
import { ExperienceType } from "@/features/experience/model";
import ExperienceCard from "./ExperienceCard";
import { useTranslation } from "react-i18next";
import globalStyles from "@/lib/globalStyles";

type ExperienceListProps = {
  experiences: ExperienceType[];
  isLoading?: boolean;
  isRefreshing?: boolean;
  onRefresh?: () => void;
  onLoadMore?: () => void;
  hasNextPage?: boolean;
  isLoadingMore?: boolean;
  onExperiencePress?: (experience: ExperienceType) => void;
  onFavoritePress?: (experience: ExperienceType) => void;
  favoriteExperienceIds?: string[];
  className?: string;
  emptyStateTitle?: string;
  emptyStateDescription?: string;
  showsVerticalScrollIndicator?: boolean;
};

const ExperienceList = ({
  experiences,
  isLoading = false,
  isRefreshing = false,
  onRefresh,
  onLoadMore,
  hasNextPage = false,
  isLoadingMore = false,
  onExperiencePress,
  onFavoritePress,
  favoriteExperienceIds = [],
  className,
  emptyStateTitle,
  emptyStateDescription,
  showsVerticalScrollIndicator = false,
}: ExperienceListProps) => {
  const { t } = useTranslation();

  const renderExperienceCard = ({ item }: { item: ExperienceType }) => {
    const isFavorite = favoriteExperienceIds.includes(item._id);

    return (
      <ExperienceCard
        experience={item}
        onPress={() => onExperiencePress?.(item)}
        onFavoritePress={() => onFavoritePress?.(item)}
        isFavorite={isFavorite}
        style={{ marginBottom: 20 }}
      />
    );
  };

  const renderEmptyState = () => {
    if (isLoading) {
      return (
        <View className="flex-1 justify-center items-center py-lg">
          <ActivityIndicator size="large" color="#BE0068" />
          <Text className="text-light-secondary text-lg mt-xs">
            {t("common.loading")}
          </Text>
        </View>
      );
    }

    return (
      <View className="flex-1 justify-center items-center py-lg px-xs">
        <Text
          style={{
            fontSize: globalStyles.size["4xl"],
            color: globalStyles.colors.dark.primary,
            textAlign: "center",
            marginBottom: globalStyles.gap.sm,
          }}
        >
          {emptyStateTitle || t("experience.no_experiences_title")}
        </Text>
        <Text
          style={{
            fontSize: globalStyles.size.xl,
            color: globalStyles.colors.light.secondary,
            textAlign: "center",
          }}
        >
          {emptyStateDescription || t("experience.no_experiences_description")}
        </Text>
      </View>
    );
  };

  const renderFooter = () => {
    if (!isLoadingMore || !hasNextPage) return null;

    return (
      <View className="py-xs justify-center items-center">
        <ActivityIndicator size="small" color="#BE0068" />
        <Text className="text-light-secondary text-sm mt-1">
          {t("experience.loading_more")}
        </Text>
      </View>
    );
  };

  const handleEndReached = () => {
    if (hasNextPage && !isLoadingMore && !isLoading) {
      onLoadMore?.();
    }
  };

  return (
    <FlatList
      data={experiences}
      renderItem={renderExperienceCard}
      keyExtractor={(item) => item._id}
      className={cn("flex-1", className)}
      contentContainerStyle={{
        paddingHorizontal: 20,
        paddingTop: 10,
        flexGrow: 1,
      }}
      showsVerticalScrollIndicator={showsVerticalScrollIndicator}
      refreshControl={
        onRefresh ? (
          <RefreshControl
            refreshing={isRefreshing}
            onRefresh={onRefresh}
            colors={["#BE0068"]}
            tintColor="#BE0068"
          />
        ) : undefined
      }
      onEndReached={handleEndReached}
      onEndReachedThreshold={0.3}
      ListEmptyComponent={renderEmptyState}
      ListFooterComponent={renderFooter}
      removeClippedSubviews={true}
      maxToRenderPerBatch={10}
      windowSize={10}
      initialNumToRender={6}
      getItemLayout={(data, index) => ({
        length: 200, // Approximate height of each card
        offset: 220 * index, // Height + margin
        index,
      })}
    />
  );
};

export default ExperienceList;
